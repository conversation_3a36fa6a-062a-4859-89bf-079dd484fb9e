# Руководство пользователя: Ордерная схема складского учета

## Введение

Ордерная схема складского учета предоставляет расширенные возможности для точного контроля движения товаров на складе через формальные документы - приходные и расходные ордера.

## Основные принципы

### Гибридная система

Каждый склад может работать в одном из двух режимов:

1. **Обычная схема** - простой учет остатков без формальных ордеров
2. **Ордерная схема** - полный контроль движения товаров через документы

### Переключение режимов

Режим работы склада определяется настройками в таблице `warehouse_order_schemes`:
- `on_coming_from` - дата начала ордерной схемы для приемок
- `on_shipment_from` - дата начала ордерной схемы для отгрузок

## Работа с приходными ордерами

### Создание приходного ордера

1. **Создание документа-основы:**
   ```http
   POST /api/internal/warehouses/receipt-orders
   ```
   
   Создается основной документ с общей информацией (склад, дата, ответственный, основание).

2. **Добавление позиций:**
   ```http
   POST /api/internal/warehouses/receipt-order-items
   ```
   
   Для каждого товара создается отдельная позиция с указанием:
   - Товара и количества
   - Цены за единицу и общей стоимости
   - Партионных данных (номер партии, серия, срок годности)
   - Статуса качества
   - Места хранения

### Управление позициями

- **Просмотр позиций:** `GET /receipt-orders/{id}/items`
- **Редактирование:** `PUT /receipt-order-items/{id}`
- **Удаление:** `DELETE /receipt-order-items/{id}`

**Важно:** Позиции можно изменять только у непроведенных ордеров.

### Проведение ордера

```http
POST /api/internal/warehouses/receipt-orders/{id}/hold
```

При проведении:
- Создаются записи в `warehouse_items` (партии товаров)
- Создаются транзакции движения в `warehouse_transactions`
- Документ блокируется от изменений

## Работа с расходными ордерами

### Создание расходного ордера

1. **Создание документа-основы:**
   ```http
   POST /api/internal/warehouses/issue-orders
   ```
   
   Указывается причина списания и общая информация.

2. **Добавление позиций:**
   ```http
   POST /api/internal/warehouses/issue-order-items
   ```
   
   Для каждой позиции указывается:
   - Конкретная партия товара (`warehouse_item_id`)
   - Количество для списания
   - Цена и общая стоимость

### Контроль доступности

Система автоматически проверяет:
- Наличие достаточного количества в партии
- Доступность товара (не зарезервирован)
- Качество товара (не просрочен, не бракованный)

## Партионный учет

### Основные поля партии

- **batch_number** - номер партии производителя
- **lot_number** - номер лота/серии
- **expiry_date** - срок годности
- **supplier_batch** - номер партии поставщика
- **quality_status** - статус качества:
  - `good` - годный товар
  - `defective` - бракованный
  - `quarantine` - на карантине
  - `expired` - просроченный

### FIFO логика

При списании товаров система автоматически применяет принцип FIFO (первый пришел - первый ушел), выбирая самые старые партии для списания.

## Резервирование товаров

### Типы резервов

- **order** - под заказы клиентов
- **production** - под производство
- **transfer** - под перемещения
- **marketing** - под маркетинговые акции
- **quality** - на контроль качества

### Приоритеты резервов

1. **1-3** - Критичные заказы (VIP клиенты)
2. **4-6** - Обычные заказы клиентов
3. **7-8** - Внутренние нужды
4. **9-10** - Маркетинговые акции

### Автоматическое снятие резервов

Резервы могут автоматически сниматься:
- По истечении срока (`expires_at`)
- При нехватке товара для более приоритетных резервов
- При отмене заказа-основания

## Валидация и контроль

### Правила ордерной схемы

- Нельзя изменять проведенные документы
- Нельзя списывать больше, чем есть в наличии
- Нельзя резервировать просроченный или бракованный товар
- Обязательна привязка к конкретным партиям

### Уведомления

Система отправляет уведомления о:
- Попытках нарушения правил
- Просроченных резервах
- Товарах с истекающим сроком годности
- Критически низких остатках

## Отчетность

### Доступные отчеты

1. **Движение товаров** - обороты по приходу/расходу
2. **Отчет по резервам** - активные и просроченные резервы
3. **Отчет по партиям** - движение конкретных партий
4. **Контрольные отчеты** - нарушения и ошибки

### Аналитика

- Процент товаров под резервом
- Средний срок резервирования
- Оборачиваемость товаров по партиям
- Эффективность использования складских площадей

## Интеграция с маркетплейсами

### Автоматическое резервирование

При синхронизации заказов с Ozon/Wildberries система автоматически:
- Резервирует товары под заказы
- Применяет FIFO логику
- Контролирует доступность остатков

### Особенности FBS/FBO

- **FBS заказы** - требуют резервирования и подтверждения
- **FBO заказы** - обрабатываются без резервирования

## Рекомендации по использованию

### Настройка ордерной схемы

1. Начните с одного склада для тестирования
2. Настройте даты начала работы схемы
3. Обучите персонал новым процессам
4. Постепенно переводите остальные склады

### Оптимизация производительности

- Используйте индексы для поиска по партиям
- Регулярно архивируйте старые транзакции
- Настройте автоматическое снятие просроченных резервов
- Мониторьте производительность операций

### Безопасность

- Настройте права доступа по ролям
- Ведите аудит всех операций
- Регулярно проводите сверки остатков
- Создавайте резервные копии данных

## Устранение неполадок

### Частые проблемы

1. **Отрицательные остатки** - проверьте настройки валидации
2. **Медленные операции** - оптимизируйте индексы
3. **Ошибки резервирования** - проверьте доступность товаров
4. **Проблемы с партиями** - проверьте корректность данных

### Диагностика

Используйте встроенные отчеты для выявления:
- Товаров с отрицательными остатками
- Резервов без покрытия
- Нарушений правил ордерной схемы
- Товаров с истекающим сроком годности
