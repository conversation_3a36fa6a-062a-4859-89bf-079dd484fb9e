# Руководство разработчика: Ордерная схема складского учета

## Обзор архитектуры

Ордерная схема складского учета реализована как гибридная система, позволяющая складам работать в двух режимах:
- **Обычная схема** - простой учет остатков
- **Ордерная схема** - полный контроль через формальные документы

## Структура проекта

### Основные компоненты

```
app/
├── Services/Api/Internal/WarehouseOrderScheme/
│   ├── WarehouseOrderSchemeDetectionService.php
│   ├── ValidationService.php
│   ├── WarehouseReceiptOrdersService/
│   ├── WarehouseReceiptOrderItemsService/
│   ├── WarehouseIssueOrdersService/
│   ├── WarehouseIssueOrderItemsService/
│   └── WarehouseReservationsService/
├── Http/Controllers/Api/Internal/WarehouseOrderScheme/
├── DTO/WarehouseOrderScheme/
├── Policies/WarehouseOrderScheme/
└── Entities/
```

### Ключевые принципы

1. **Отдельные API для позиций** - позиции ордеров управляются через отдельные endpoints
2. **Автоматический пересчет итогов** - при изменении позиций обновляются итоги ордера
3. **Условная валидация** - правила зависят от режима работы склада
4. **Партионный учет** - каждая партия товара отслеживается отдельно

## Работа с позициями ордеров

### Архитектура позиций

После рефакторинга позиции ордеров работают аналогично acceptances:

```php
// Создание позиции приходного ордера
POST /api/internal/warehouses/receipt-order-items
{
    "warehouse_receipt_order_id": "uuid",
    "product_id": "uuid", 
    "quantity": 100,
    "unit_price": "150.00",
    "total_price": "15000.00",
    "batch_number": "BATCH001"
}
```

### Компоненты для позиций

1. **DTO**: `WarehouseReceiptOrderItemStoreDTO`, `WarehouseReceiptOrderItemUpdateDTO`
2. **Request**: `WarehouseReceiptOrderItemStoreRequest`, `WarehouseReceiptOrderItemUpdateRequest`
3. **Controller**: `WarehouseReceiptOrderItemsController`
4. **Service**: `WarehouseReceiptOrderItemsService`
5. **Policy**: `WarehouseReceiptOrderItemPolicy`

## Определение режима работы

### Сервис детекции

```php
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;

$isActive = $detectionService->isOrderSchemeActive($warehouseId, 'receipts', $date);
```

### Настройки склада

```sql
-- Таблица warehouse_order_schemes
CREATE TABLE warehouse_order_schemes (
    warehouse_id UUID PRIMARY KEY,
    on_coming_from DATE,           -- Дата начала ордерной схемы для приемок
    on_shipment_from DATE,         -- Дата начала ордерной схемы для отгрузок
    control_operational_balances BOOLEAN DEFAULT false
);
```

## Валидация

### Условная валидация

```php
// В ValidationService
public function validateReceiptOrder(array $data, string $warehouseId): void
{
    if ($this->detectionService->isOrderSchemeActive($warehouseId, 'receipts')) {
        // Валидация для ордерной схемы
        $this->validateOrderSchemeRules($data);
    } else {
        // Валидация для обычной схемы
        $this->validateRegularSchemeRules($data);
    }
}
```

### Правила ордерной схемы

- Нельзя изменять проведенные документы
- Обязательна привязка к конкретным партиям для расходных ордеров
- Проверка доступности товара при резервировании
- Контроль сроков годности и качества

## Партионный учет

### Структура warehouse_items

```sql
CREATE TABLE warehouse_items (
    id UUID PRIMARY KEY,
    warehouse_id UUID NOT NULL,
    product_id UUID NOT NULL,
    quantity INTEGER NOT NULL,
    available_quantity INTEGER NOT NULL,
    reserved_quantity INTEGER DEFAULT 0,
    batch_number VARCHAR(255),
    lot_number VARCHAR(255),
    expiry_date DATE,
    quality_status VARCHAR(50) DEFAULT 'good',
    received_at TIMESTAMP
);
```

### FIFO логика

```php
// Автоматический выбор партий для списания
$items = $this->warehouseItemsRepository->getForFIFO($warehouseId, $productId, $quantity);
```

## Резервирование

### Типы резервов

- `order` - под заказы клиентов
- `production` - под производство  
- `transfer` - под перемещения
- `marketing` - под маркетинговые акции
- `quality` - на контроль качества

### Приоритеты

1-3: Критичные заказы
4-6: Обычные заказы
7-8: Внутренние нужды
9-10: Маркетинговые акции

## Транзакции движения

### Структура warehouse_transactions

```sql
CREATE TABLE warehouse_transactions (
    id UUID PRIMARY KEY,
    warehouse_id UUID NOT NULL,
    product_id UUID NOT NULL,
    warehouse_item_id UUID,
    transaction_type VARCHAR(50) NOT NULL, -- 'receipt', 'issue', 'reserve', 'unreserve'
    quantity INTEGER NOT NULL,
    document_type VARCHAR(100),
    document_id UUID,
    created_at TIMESTAMP
);
```

## Тестирование

### Интеграционные тесты

```php
// tests/Feature/WarehouseOrderSchemeIntegrationTest.php
public function test_complete_order_scheme_workflow(): void
{
    // 1. Создание приходного ордера
    // 2. Добавление позиций
    // 3. Проведение ордера
    // 4. Создание расходного ордера
    // 5. Списание товаров
}
```

### Тестирование позиций

```php
// tests/Feature/WarehouseReceiptOrderItemsTest.php
public function test_creates_receipt_order_item_separately(): void
{
    // Тест создания позиции через отдельный API
}
```

## Производительность

### Оптимизация запросов

- Индексы на `warehouse_id`, `product_id`, `batch_number`
- Пагинация для списков позиций
- Кэширование настроек ордерной схемы

### Мониторинг

- Время выполнения FIFO операций
- Количество ошибок валидации
- Производительность резервирования

## Безопасность

### Авторизация

```php
// Политики для позиций ордеров
class WarehouseReceiptOrderItemPolicy
{
    public function create(User $user, WarehouseReceiptOrderItemStoreDTO $dto): void
    {
        // Проверка прав доступа к ордеру и товару
    }
}
```

### Аудит

- Логирование всех операций с ордерами
- Отслеживание изменений позиций
- Мониторинг подозрительной активности

## Интеграция с маркетплейсами

### Автоматическое резервирование

```php
// При синхронизации заказов Ozon/Wildberries
$this->reservationService->reserveForOrder($orderId, $items);
```

### Особенности FBS/FBO

- FBS заказы требуют резервирования
- FBO заказы обрабатываются без резервирования
- Автоматическое снятие резервов при отмене

## Миграция и обновления

### Руководство по миграции

См. [warehouse-order-scheme-migration-guide.md](warehouse-order-scheme-migration-guide.md)

### Обратная совместимость

- Существующие API продолжают работать
- Постепенный переход на новую архитектуру
- Поддержка старых интеграций

## Устранение неполадок

### Частые проблемы

1. **Отрицательные остатки** - проверить настройки валидации
2. **Ошибки резервирования** - проверить доступность товаров
3. **Медленные операции** - оптимизировать индексы

### Диагностика

```php
// Проверка состояния ордерной схемы
GET /api/internal/warehouse-order-scheme/detection/is-active?warehouse_id=uuid

// Отчеты по нарушениям
GET /api/internal/warehouse-order-scheme/reports/violations
```

## Дополнительные ресурсы

- [API документация](warehouse-order-scheme-api-documentation.md)
- [Руководство пользователя](warehouse-order-scheme-user-guide.md)
- [Устранение неполадок](warehouse-order-scheme-troubleshooting.md)
- [Руководство по миграции](warehouse-order-scheme-migration-guide.md)
