# Руководство по миграции: Отдельные API для позиций ордеров

## Обзор изменений

В рамках рефакторинга архитектуры была изменена логика работы с позициями ордеров. Теперь позиции управляются через отдельные API endpoints, аналогично тому, как это реализовано с приемками (acceptances).

## Что изменилось

### До рефакторинга

```json
// Создание приходного ордера с позициями
POST /api/internal/warehouses/receipt-orders
{
  "warehouse_id": "uuid",
  "employee_id": "uuid",
  "total_quantity": 100,
  "total_cost": "15000.00",
  "items": [
    {
      "product_id": "uuid",
      "quantity": 100,
      "unit_price": "150.00",
      "total_price": "15000.00"
    }
  ]
}
```

### После рефакторинга

```json
// 1. Создание приходного ордера БЕЗ позиций
POST /api/internal/warehouses/receipt-orders
{
  "warehouse_id": "uuid",
  "employee_id": "uuid",
  "total_quantity": 0,
  "total_cost": "0.00"
}

// 2. Добавление позиций отдельно
POST /api/internal/warehouses/receipt-order-items
{
  "warehouse_receipt_order_id": "uuid",
  "product_id": "uuid",
  "quantity": 100,
  "unit_price": "150.00",
  "total_price": "15000.00"
}
```

## Новые API endpoints

### Позиции приходных ордеров

- `GET /receipt-orders/{id}/items` - список позиций
- `POST /receipt-order-items` - создание позиции
- `GET /receipt-order-items/{id}` - просмотр позиции
- `PUT /receipt-order-items/{id}` - обновление позиции
- `DELETE /receipt-order-items/{id}` - удаление позиции

### Позиции расходных ордеров

- `GET /issue-orders/{id}/items` - список позиций
- `POST /issue-order-items` - создание позиции
- `GET /issue-order-items/{id}` - просмотр позиции
- `PUT /issue-order-items/{id}` - обновление позиции
- `DELETE /issue-order-items/{id}` - удаление позиции

## Автоматические возможности

### Пересчет итогов

При изменении позиций автоматически пересчитываются:
- `total_quantity` - общее количество
- `total_cost` - общая стоимость

### Валидация

- Нельзя изменять позиции проведенных ордеров
- Проверка доступности товара для расходных ордеров
- Проверка прав доступа к складу и товарам

## Миграция существующего кода

### Frontend изменения

```javascript
// Старый код
const createReceiptOrder = async (orderData) => {
  return await api.post('/receipt-orders', {
    ...orderData,
    items: orderItems
  });
};

// Новый код
const createReceiptOrder = async (orderData) => {
  // 1. Создаем ордер
  const order = await api.post('/receipt-orders', orderData);
  
  // 2. Добавляем позиции
  for (const item of orderItems) {
    await api.post('/receipt-order-items', {
      warehouse_receipt_order_id: order.id,
      ...item
    });
  }
  
  return order;
};
```

### Backend изменения

```php
// Старый DTO
class WarehouseReceiptOrderStoreDTO
{
    public function __construct(
        // ... другие поля
        public readonly ?array $items = null // УДАЛЕНО
    ) {}
}

// Новый DTO для позиций
class WarehouseReceiptOrderItemStoreDTO
{
    public function __construct(
        public readonly string $warehouseReceiptOrderId,
        public readonly string $productId,
        public readonly int $quantity,
        public readonly string $unitPrice,
        public readonly string $totalPrice,
        // ... другие поля
    ) {}
}
```

## Преимущества новой архитектуры

### 1. Соответствие архитектуре проекта
- Единообразие с acceptances
- Тонкие контроллеры
- Отдельные политики для позиций

### 2. Улучшенная производительность
- Возможность пагинации позиций
- Частичное обновление данных
- Оптимизированные запросы

### 3. Лучший UX
- Возможность добавлять позиции по одной
- Редактирование отдельных позиций
- Поиск и фильтрация позиций

### 4. Расширяемость
- Легко добавлять новые поля к позициям
- Отдельная валидация для позиций
- Независимое тестирование

## Обратная совместимость

### Что сохранилось
- Структура базы данных
- Основные API ордеров
- Логика проведения документов
- Система резервирования

### Что изменилось
- Убрано поле `items` из DTO ордеров
- Добавлены новые API для позиций
- Обновлена документация

## Тестирование

### Новые тесты
- `WarehouseReceiptOrderItemsTest` - тесты позиций приходных ордеров
- `WarehouseIssueOrderItemsTest` - тесты позиций расходных ордеров
- `WarehouseOrderSchemeIntegrationTest` - интеграционные тесты

### Обновленные тесты
- Удалены тесты с `items` в теле запроса
- Добавлены тесты отдельного создания позиций
- Тесты валидации и авторизации

## Мониторинг и отладка

### Логирование
- Создание/обновление/удаление позиций
- Автоматический пересчет итогов
- Ошибки валидации

### Метрики
- Время выполнения операций с позициями
- Количество позиций на ордер
- Частота изменений позиций

## Рекомендации

### Для разработчиков
1. Используйте новые API для всех новых интеграций
2. Тестируйте пересчет итогов при изменении позиций
3. Проверяйте права доступа для каждой операции

### Для тестировщиков
1. Проверьте создание ордеров без позиций
2. Тестируйте добавление/изменение/удаление позиций
3. Проверьте валидацию проведенных ордеров

### Для DevOps
1. Мониторьте производительность новых endpoints
2. Настройте алерты на ошибки валидации
3. Проверьте логи автоматического пересчета итогов

## Поддержка

При возникновении вопросов или проблем:
1. Проверьте документацию API
2. Изучите интеграционные тесты
3. Обратитесь к руководству по устранению неполадок
