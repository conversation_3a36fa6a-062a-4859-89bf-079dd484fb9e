# API Документация: Ордерная схема складского учета

## Обзор API

Ордерная схема предоставляет REST API для управления складскими операциями с расширенной функциональностью.

**Базовый URL:** `/api/internal/warehouses/`

**Авторизация:** Bearer <PERSON>ken (требуется для всех запросов)

## Определение режима работы

### Проверка активности ордерной схемы

```http
GET /api/internal/warehouses/detection/is-active
```

**Параметры:**
- `warehouse_id` (required) - UUID склада
- `operation_type` (required) - тип операции: `acceptances` или `shipments`
- `date` (optional) - дата операции (по умолчанию текущая)

**Ответ:**
```json
{
  "is_active": true,
  "warehouse_id": "uuid",
  "operation_type": "shipments",
  "date": "2024-01-15"
}
```

## Приходные ордера

### Список приходных ордеров

```http
GET /api/internal/warehouses/receipt-orders
```

**Параметры фильтрации:**
- `cabinet_id` (required) - UUID кабинета
- `filters[warehouses][value][]` - массив UUID складов
- `filters[employees][value][]` - массив UUID сотрудников
- `filters[departments][value][]` - массив UUID подразделений
- `filters[statuses][value][]` - массив UUID статусов
- `filters[is_held][value]` - boolean, проведенные документы
- `filters[search][value]` - поиск по тексту
- `filters[period][from]` - дата начала периода (d.m.Y H:i)
- `filters[period][to]` - дата окончания периода (d.m.Y H:i)
- `sortField` - поле сортировки
- `sortDirection` - направление сортировки (asc/desc)
- `page` - номер страницы
- `per_page` - количество на странице (макс. 100)

### Создание приходного ордера

```http
POST /api/internal/warehouses/receipt-orders
```

**Тело запроса:**
```json
{
  "cabinet_id": "uuid",
  "employee_id": "uuid",
  "department_id": "uuid",
  "warehouse_id": "uuid",
  "number": "ПО-001",
  "date_from": "2024-01-15",
  "status_id": "uuid",
  "document_basis_type": "invoice",
  "document_basis_id": "uuid",
  "reason": "Поступление от поставщика",
  "total_quantity": 100,
  "total_cost": "15000.00",
  "comment": "Комментарий"
}
```

**Примечание:** Позиции ордера (`items`) теперь управляются отдельно через API позиций (см. раздел "Позиции приходных ордеров").

### Проведение приходного ордера

```http
POST /api/internal/warehouses/receipt-orders/{id}/hold
```

## Позиции приходных ордеров

### Список позиций приходного ордера

```http
GET /api/internal/warehouses/receipt-orders/{id}/items
```

**Параметры:**
- `id` (path) - UUID приходного ордера
- `filters[search][value]` - поиск по названию товара
- `sortField` - поле сортировки
- `sortDirection` - направление сортировки (asc/desc)
- `page` - номер страницы
- `per_page` - количество на странице

### Создание позиции приходного ордера

```http
POST /api/internal/warehouses/receipt-order-items
```

**Тело запроса:**
```json
{
  "warehouse_receipt_order_id": "uuid",
  "product_id": "uuid",
  "quantity": 50,
  "unit_price": "150.00",
  "total_price": "7500.00",
  "batch_number": "BATCH001",
  "lot_number": "LOT001",
  "expiry_date": "2024-12-31",
  "supplier_batch": "SUP001",
  "quality_status": "good",
  "storage_location": "A1-01",
  "comment": "Комментарий к позиции"
}
```

### Обновление позиции приходного ордера

```http
PUT /api/internal/warehouses/receipt-order-items/{id}
```

### Удаление позиции приходного ордера

```http
DELETE /api/internal/warehouses/receipt-order-items/{id}
```

**Примечание:** Позиции можно изменять только у непроведенных ордеров. При изменении позиций автоматически пересчитываются итоги ордера.

## Расходные ордера

### Список расходных ордеров

```http
GET /api/internal/warehouses/issue-orders
```

**Дополнительные фильтры:**
- `filters[write_off_reasons][value][]` - причины списания

### Создание расходного ордера

```http
POST /api/internal/warehouses/issue-orders
```

**Тело запроса:**
```json
{
  "cabinet_id": "uuid",
  "employee_id": "uuid",
  "department_id": "uuid",
  "warehouse_id": "uuid",
  "number": "РО-001",
  "date_from": "2024-01-15",
  "write_off_reason": "defective",
  "reason_description": "Обнаружен брак при проверке",
  "total_quantity": 10,
  "total_cost": "1500.00",
  "comment": "Комментарий"
}
```

**Примечание:** Позиции ордера (`items`) теперь управляются отдельно через API позиций (см. раздел "Позиции расходных ордеров").

### Справочник причин списания

```http
GET /api/internal/warehouses/issue-orders/write-off-reasons
```

**Ответ:**
```json
{
  "reasons": {
    "defective": "Брак",
    "expired": "Истек срок годности",
    "shortage": "Недостача",
    "internal_use": "Внутренние нужды",
    "return_to_supplier": "Возврат поставщику",
    "damage": "Повреждение",
    "other": "Прочее"
  }
}
```

## Позиции расходных ордеров

### Список позиций расходного ордера

```http
GET /api/internal/warehouses/issue-orders/{id}/items
```

**Параметры:**
- `id` (path) - UUID расходного ордера
- `filters[search][value]` - поиск по названию товара
- `sortField` - поле сортировки
- `sortDirection` - направление сортировки (asc/desc)
- `page` - номер страницы
- `per_page` - количество на странице

### Создание позиции расходного ордера

```http
POST /api/internal/warehouses/issue-order-items
```

**Тело запроса:**
```json
{
  "warehouse_issue_order_id": "uuid",
  "warehouse_item_id": "uuid",
  "quantity": 10,
  "unit_price": "150.00",
  "total_price": "1500.00",
  "comment": "Комментарий к позиции"
}
```

### Обновление позиции расходного ордера

```http
PUT /api/internal/warehouses/issue-order-items/{id}
```

### Удаление позиции расходного ордера

```http
DELETE /api/internal/warehouses/issue-order-items/{id}
```

**Примечание:** Позиции можно изменять только у непроведенных ордеров. При изменении позиций автоматически пересчитываются итоги ордера. Система проверяет доступность товара в указанной партии.

## Резервирование товаров

### Создание резерва

```http
POST /api/internal/warehouses/reservations
```

**Тело запроса:**
```json
{
  "warehouse_item_id": "uuid",
  "customer_order_item_id": "uuid",
  "reserved_quantity": 5,
  "reservation_type": "order",
  "document_type": "customer_order",
  "document_id": "uuid",
  "priority": 5,
  "expires_at": "2024-02-15 23:59:59",
  "auto_release": true
}
```

### Отмена резерва

```http
POST /api/internal/warehouses/reservations/{id}/cancel
```

### Отметка резервов как отгруженных

```http
POST /api/internal/warehouses/reservations/mark-as-shipped
```

**Тело запроса:**
```json
{
  "reservation_ids": ["uuid1", "uuid2", "uuid3"]
}
```

### Освобождение истекших резервов

```http
POST /api/internal/warehouses/reservations/release-expired
```

### Проверка доступности товара

```http
GET /api/internal/warehouses/reservations/availability
```

**Параметры:**
- `product_id` (required) - UUID товара
- `warehouse_id` (required) - UUID склада
- `date` (optional) - дата проверки

**Ответ:**
```json
{
  "available": 150,
  "reserved": 50,
  "total": 200
}
```

### Справочник типов резервирования

```http
GET /api/internal/warehouses/reservations/types
```

## Отчеты и аналитика

### Отчет по движению товаров

```http
GET /api/internal/warehouses/reports/stock-movement
```

**Параметры:**
- `warehouse_id` (required) - UUID склада
- `date_from` (optional) - дата начала периода
- `date_to` (optional) - дата окончания периода
- `product_id` (optional) - UUID товара

### Отчет по резервированию

```http
GET /api/internal/warehouses/reports/reservations
```

**Параметры:**
- `warehouse_id` (required) - UUID склада
- `date_from` (optional) - дата начала периода
- `date_to` (optional) - дата окончания периода
- `reservation_type` (optional) - тип резервирования
- `status` (optional) - статус резерва

### Аналитика ордерной схемы

```http
GET /api/internal/warehouses/reports/analytics
```

**Параметры:**
- `warehouse_id` (required) - UUID склада
- `date_from` (optional) - дата начала периода
- `date_to` (optional) - дата окончания периода

### Отчет по остаткам

```http
GET /api/internal/warehouses/reports/inventory
```

**Параметры:**
- `warehouse_id` (required) - UUID склада
- `product_id` (optional) - UUID товара
- `quality_status` (optional) - статус качества
- `include_expired` (optional) - включать просроченные товары

## Валидация операций

### Валидация отгрузки

```http
POST /api/internal/warehouses/validation/shipment
```

**Тело запроса:**
```json
{
  "warehouse_id": "uuid",
  "items": [
    {
      "product_id": "uuid",
      "quantity": 10
    }
  ]
}
```

**Ответ:**
```json
{
  "valid": false,
  "errors": [
    {
      "product_id": "uuid",
      "type": "insufficient_reservation",
      "message": "Недостаточно зарезервированного товара",
      "required": 10,
      "available": 5
    }
  ],
  "warnings": [
    {
      "product_id": "uuid",
      "type": "expiry_warning",
      "message": "Часть товара истекает в течение 7 дней",
      "quantity": 3
    }
  ]
}
```

## Коды ошибок

| Код | Описание |
|-----|----------|
| `400` | Неверный запрос |
| `401` | Не авторизован |
| `403` | Доступ запрещен |
| `404` | Ресурс не найден |
| `422` | Ошибка валидации |
| `500` | Внутренняя ошибка сервера |

## Примеры использования

### Создание полного цикла документооборота

```javascript
// 1. Проверяем активность ордерной схемы для приемок
const isActive = await fetch('/api/internal/warehouses/detection/is-active', {
  method: 'GET',
  params: {
    warehouse_id: 'warehouse-uuid',
    operation_type: 'acceptances',
    date: '2024-01-15'
  }
});

// 2. Создаем приходный ордер
const receiptOrder = await fetch('/api/internal/warehouses/receipt-orders', {
  method: 'POST',
  body: JSON.stringify({
    warehouse_id: 'warehouse-uuid',
    employee_id: 'employee-uuid',
    // ... остальные поля
  })
});

// 3. Проводим приходный ордер
await fetch(`/api/internal/warehouses/receipt-orders/${receiptOrder.id}/hold`, {
  method: 'POST'
});

// 4. Создаем резерв под заказ
const reservation = await fetch('/api/internal/warehouses/reservations', {
  method: 'POST',
  body: JSON.stringify({
    warehouse_item_id: 'item-uuid',
    customer_order_item_id: 'order-item-uuid',
    reserved_quantity: 5,
    reservation_type: 'order'
  })
});
```

### Получение аналитики

```javascript
// Получаем полную аналитику по складу
const analytics = await fetch('/api/internal/warehouses/reports/analytics', {
  method: 'GET',
  params: {
    warehouse_id: 'warehouse-uuid',
    date_from: '2024-01-01',
    date_to: '2024-01-31'
  }
});

console.log('Статистика документов:', analytics.document_statistics);
console.log('Эффективность резервирования:', analytics.reservation_efficiency);
console.log('Оборачиваемость:', analytics.inventory_turnover);
```
