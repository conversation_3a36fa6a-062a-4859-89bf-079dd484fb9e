<?php

namespace App\DTO\WarehouseOrderScheme;

use App\Contracts\DtoContract;

class WarehouseReceiptOrderItemUpdateDTO implements DtoContract
{
    public function __construct(
        public readonly string $resourceId,
        public readonly int $quantity,
        public readonly string $unitPrice,
        public readonly string $totalPrice,
        public readonly int $userId,
        public readonly ?string $batchNumber = null,
        public readonly ?string $lotNumber = null,
        public readonly ?string $expiryDate = null,
        public readonly ?string $supplierBatch = null,
        public readonly string $qualityStatus = 'good',
        public readonly ?string $storageLocation = null,
        public readonly ?string $comment = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            resourceId: $data['resource_id'],
            quantity: (int)$data['quantity'],
            unitPrice: (string)$data['unit_price'],
            totalPrice: (string)$data['total_price'],
            userId: auth()->user()->id,
            batchNumber: $data['batch_number'] ?? null,
            lotNumber: $data['lot_number'] ?? null,
            expiryDate: $data['expiry_date'] ?? null,
            supplierBatch: $data['supplier_batch'] ?? null,
            qualityStatus: $data['quality_status'] ?? 'good',
            storageLocation: $data['storage_location'] ?? null,
            comment: $data['comment'] ?? null
        );
    }
}
