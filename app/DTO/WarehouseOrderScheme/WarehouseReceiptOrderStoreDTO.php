<?php

namespace App\DTO\WarehouseOrderScheme;

use App\Contracts\DtoContract;

class WarehouseReceiptOrderStoreDTO implements DtoContract
{
    public function __construct(
        public readonly ?string $cabinetId,
        public readonly string $employeeId,
        public readonly string $departmentId,
        public readonly string $warehouseId,
        public readonly string $dateFrom,
        public readonly int $totalQuantity,
        public readonly string $totalCost,
        public readonly int $userId,
        public readonly ?string $number = null,
        public readonly ?string $statusId = null,
        public readonly ?string $documentBasisType = null,
        public readonly ?string $documentBasisId = null,
        public readonly ?string $reason = null,
        public readonly ?string $comment = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            employeeId: $data['employee_id'],
            departmentId: $data['department_id'],
            warehouseId: $data['warehouse_id'],
            dateFrom: $data['date_from'],
            totalQuantity: (int)$data['total_quantity'],
            totalCost: (string)$data['total_cost'],
            userId: auth()->user()->id,
            number: $data['number'] ?? null,
            statusId: $data['status_id'] ?? null,
            documentBasisType: $data['document_basis_type'] ?? null,
            documentBasisId: $data['document_basis_id'] ?? null,
            reason: $data['reason'] ?? null,
            comment: $data['comment'] ?? null
        );
    }
}
