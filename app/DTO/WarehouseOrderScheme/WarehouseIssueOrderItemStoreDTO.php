<?php

namespace App\DTO\WarehouseOrderScheme;

use App\Contracts\DtoContract;

class WarehouseIssueOrderItemStoreDTO implements DtoContract
{
    public function __construct(
        public readonly string $warehouseIssueOrderId,
        public readonly string $warehouseItemId,
        public readonly int $quantity,
        public readonly string $unitPrice,
        public readonly string $totalPrice,
        public readonly int $userId,
        public readonly ?string $comment = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            warehouseIssueOrderId: $data['warehouse_issue_order_id'],
            warehouseItemId: $data['warehouse_item_id'],
            quantity: (int)$data['quantity'],
            unitPrice: (string)$data['unit_price'],
            totalPrice: (string)$data['total_price'],
            userId: auth()->user()->id,
            comment: $data['comment'] ?? null
        );
    }
}
