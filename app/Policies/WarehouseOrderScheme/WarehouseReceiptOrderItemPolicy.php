<?php

namespace App\Policies\WarehouseOrderScheme;

use App\Contracts\DtoContract;
use App\Contracts\Policies\WarehouseOrderScheme\WarehouseReceiptOrderItemPolicyContract;
use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Contracts\Services\Internal\AuthorizationServiceContract;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemStoreDTO;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemUpdateDTO;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;

class WarehouseReceiptOrderItemPolicy implements WarehouseReceiptOrderItemPolicyContract
{
    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly WarehouseReceiptOrdersRepositoryContract $warehouseReceiptOrdersRepository,
        private readonly WarehouseReceiptOrderItemsRepositoryContract $warehouseReceiptOrderItemsRepository
    ) {
    }

    public function index(string $warehouseReceiptOrderId): void
    {
        $order = $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $warehouseReceiptOrderId,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'view'
        );
    }

    public function view(User $user, string $id): void
    {
        $item = $this->warehouseReceiptOrderItemsRepository->show($id);
        
        if (!$item) {
            throw new AccessDeniedException('Позиция приходного ордера не найдена');
        }

        $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $item->warehouse_receipt_order_id,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'view'
        );
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseReceiptOrderItemStoreDTO) {
            return;
        }

        $order = $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $dto->warehouseReceiptOrderId,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'update'
        );

        if ($order->held) {
            throw new AccessDeniedException('Нельзя добавлять позиции в проведенный приходный ордер');
        }

        $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $order->cabinet_id,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseReceiptOrderItemUpdateDTO) {
            return;
        }

        $item = $this->warehouseReceiptOrderItemsRepository->show($dto->resourceId);
        
        if (!$item) {
            throw new AccessDeniedException('Позиция приходного ордера не найдена');
        }

        $order = $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $item->warehouse_receipt_order_id,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'update'
        );

        if ($order->held) {
            throw new AccessDeniedException('Нельзя изменять позиции проведенного приходного ордера');
        }
    }

    public function delete(User $user, string $id): void
    {
        $item = $this->warehouseReceiptOrderItemsRepository->show($id);
        
        if (!$item) {
            throw new AccessDeniedException('Позиция приходного ордера не найдена');
        }

        $order = $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $item->warehouse_receipt_order_id,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'update'
        );

        if ($order->held) {
            throw new AccessDeniedException('Нельзя удалять позиции проведенного приходного ордера');
        }
    }
}
