<?php

namespace App\Policies\WarehouseOrderScheme;

use App\Contracts\DtoContract;
use App\Contracts\Policies\WarehouseOrderScheme\WarehouseIssueOrderItemPolicyContract;
use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Contracts\Services\Internal\AuthorizationServiceContract;
use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderItemStoreDTO;
use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderItemUpdateDTO;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;

class WarehouseIssueOrderItemPolicy implements WarehouseIssueOrderItemPolicyContract
{
    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly WarehouseIssueOrdersRepositoryContract $warehouseIssueOrdersRepository,
        private readonly WarehouseIssueOrderItemsRepositoryContract $warehouseIssueOrderItemsRepository
    ) {
    }

    public function index(string $warehouseIssueOrderId): void
    {
        $order = $this->authService->validateRelationAccess(
            'warehouse_issue_orders',
            $warehouseIssueOrderId,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'view'
        );
    }

    public function view(User $user, string $id): void
    {
        $item = $this->warehouseIssueOrderItemsRepository->show($id);
        
        if (!$item) {
            throw new AccessDeniedException('Позиция расходного ордера не найдена');
        }

        $this->authService->validateRelationAccess(
            'warehouse_issue_orders',
            $item->warehouse_issue_order_id,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'view'
        );
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseIssueOrderItemStoreDTO) {
            return;
        }

        $order = $this->authService->validateRelationAccess(
            'warehouse_issue_orders',
            $dto->warehouseIssueOrderId,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'update'
        );

        if ($order->held) {
            throw new AccessDeniedException('Нельзя добавлять позиции в проведенный расходный ордер');
        }

        $this->authService->validateRelationAccess(
            'warehouse_items',
            $dto->warehouseItemId,
            $order->cabinet_id,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'view'
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseIssueOrderItemUpdateDTO) {
            return;
        }

        $item = $this->warehouseIssueOrderItemsRepository->show($dto->resourceId);
        
        if (!$item) {
            throw new AccessDeniedException('Позиция расходного ордера не найдена');
        }

        $order = $this->authService->validateRelationAccess(
            'warehouse_issue_orders',
            $item->warehouse_issue_order_id,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'update'
        );

        if ($order->held) {
            throw new AccessDeniedException('Нельзя изменять позиции проведенного расходного ордера');
        }
    }

    public function delete(User $user, string $id): void
    {
        $item = $this->warehouseIssueOrderItemsRepository->show($id);
        
        if (!$item) {
            throw new AccessDeniedException('Позиция расходного ордера не найдена');
        }

        $order = $this->authService->validateRelationAccess(
            'warehouse_issue_orders',
            $item->warehouse_issue_order_id,
            null,
            PermissionNameEnum::WAREHOUSE_ORDER_SCHEME->value,
            'update'
        );

        if ($order->held) {
            throw new AccessDeniedException('Нельзя удалять позиции проведенного расходного ордера');
        }
    }
}
