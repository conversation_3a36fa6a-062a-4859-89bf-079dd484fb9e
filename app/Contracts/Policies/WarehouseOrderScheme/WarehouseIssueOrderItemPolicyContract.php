<?php

namespace App\Contracts\Policies\WarehouseOrderScheme;

use App\Contracts\DtoContract;
use App\Models\User;

interface WarehouseIssueOrderItemPolicyContract
{
    public function index(string $warehouseIssueOrderId): void;

    public function view(User $user, string $id): void;

    public function create(User $user, DtoContract $dto): void;

    public function update(User $user, DtoContract $dto): void;

    public function delete(User $user, string $id): void;
}
