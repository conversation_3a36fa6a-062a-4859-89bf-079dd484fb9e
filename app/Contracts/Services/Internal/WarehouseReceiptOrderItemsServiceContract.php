<?php

namespace App\Contracts\Services\Internal;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

interface WarehouseReceiptOrderItemsServiceContract
{
    public function index(IndexRequestDTO $data): Collection;

    public function show(string $id): ?object;

    public function create(HasInsertArrayDtoContract $dto): string;

    public function update(HasUpdateArrayDtoContract $dto): void;

    public function delete(string $id): void;
}
