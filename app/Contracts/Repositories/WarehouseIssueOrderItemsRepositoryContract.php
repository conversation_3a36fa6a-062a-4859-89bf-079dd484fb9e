<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseIssueOrderItemsRepositoryContract
{
    public function insert(array $data): bool;

    public function bulkInsert(array $items): bool;

    public function update(string $id, array $data): int;

    public function delete(string $id): int;

    public function show(string $id): ?object;

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection;

    public function getByIssueOrder(string $issueOrderId): Collection;

    public function getByOrderId(string $orderId): Collection;

    public function deleteByIssueOrder(string $issueOrderId): int;

    public function getByWarehouseItem(string $warehouseItemId): Collection;

    public function getTotalQuantityByIssueOrder(string $issueOrderId): int;

    public function getByProduct(string $productId, array $filters = []): Collection;
}
