<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderItemStoreDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseIssueOrderItemStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'warehouse_issue_order_id' => 'required|uuid|exists:warehouse_issue_orders,id',
            'warehouse_item_id' => 'required|uuid|exists:warehouse_items,id',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|string|regex:/^\d+(\.\d{1,30})?$/',
            'total_price' => 'required|string|regex:/^\d+(\.\d{1,30})?$/',
            'comment' => 'nullable|string|max:1000',
        ];
    }

    public function toDTO(): WarehouseIssueOrderItemStoreDTO
    {
        return WarehouseIssueOrderItemStoreDTO::fromArray($this->validated());
    }
}
