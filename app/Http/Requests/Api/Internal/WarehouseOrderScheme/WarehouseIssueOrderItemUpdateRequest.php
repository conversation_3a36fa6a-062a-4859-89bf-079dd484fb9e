<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderItemUpdateDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseIssueOrderItemUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|string|regex:/^\d+(\.\d{1,30})?$/',
            'total_price' => 'required|string|regex:/^\d+(\.\d{1,30})?$/',
            'comment' => 'nullable|string|max:1000',
        ];
    }

    public function toDTO(): WarehouseIssueOrderItemUpdateDTO
    {
        return WarehouseIssueOrderItemUpdateDTO::fromArray(
            array_merge($this->validated(), ['resource_id' => $this->route('id')])
        );
    }
}
