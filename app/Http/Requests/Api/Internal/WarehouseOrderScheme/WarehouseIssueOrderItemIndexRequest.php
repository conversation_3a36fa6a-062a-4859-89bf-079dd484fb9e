<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\IndexRequestDTO;
use App\Entities\WarehouseIssueOrderItemEntity;
use App\Http\Requests\Api\Internal\BaseIndexRequest;

class WarehouseIssueOrderItemIndexRequest extends BaseIndexRequest
{
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'warehouse_issue_order_id' => 'required|uuid|exists:warehouse_issue_orders,id',
        ]);
    }

    public function toDTO(): IndexRequestDTO
    {
        return new IndexRequestDTO(
            id: $this->route('id'),
            entity: new WarehouseIssueOrderItemEntity(),
            filters: $this->validated()['filters'] ?? [],
            fields: $this->validated()['fields'] ?? ['*'],
            sortField: $this->validated()['sort_field'] ?? null,
            sortDirection: $this->validated()['sort_direction'] ?? 'asc',
            page: (int)($this->validated()['page'] ?? 1),
            perPage: (int)($this->validated()['per_page'] ?? 15)
        );
    }
}
