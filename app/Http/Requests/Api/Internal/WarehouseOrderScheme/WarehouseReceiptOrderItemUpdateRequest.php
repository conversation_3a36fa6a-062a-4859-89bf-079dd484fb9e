<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemUpdateDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseReceiptOrderItemUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|string|regex:/^\d+(\.\d{1,30})?$/',
            'total_price' => 'required|string|regex:/^\d+(\.\d{1,30})?$/',
            'batch_number' => 'nullable|string|max:255',
            'lot_number' => 'nullable|string|max:255',
            'expiry_date' => 'nullable|date',
            'supplier_batch' => 'nullable|string|max:255',
            'quality_status' => 'nullable|string|in:good,defective,quarantine,expired',
            'storage_location' => 'nullable|string|max:255',
            'comment' => 'nullable|string|max:1000',
        ];
    }

    public function toDTO(): WarehouseReceiptOrderItemUpdateDTO
    {
        return WarehouseReceiptOrderItemUpdateDTO::fromArray(
            array_merge($this->validated(), ['resource_id' => $this->route('id')])
        );
    }
}
