<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Policies\WarehouseOrderScheme\WarehouseReceiptOrderItemPolicyContract;
use App\Contracts\Services\Internal\WarehouseReceiptOrderItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemIndexRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemStoreRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemUpdateRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemCollection;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseReceiptOrderItemsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseReceiptOrderItemsServiceContract $service,
        private readonly WarehouseReceiptOrderItemPolicyContract $policy
    ) {
    }

    protected function getPolicy(): WarehouseReceiptOrderItemPolicyContract
    {
        return $this->policy;
    }

    /**
     * @response 200 WarehouseReceiptOrderItemCollection<WarehouseReceiptOrderItemResource>
     */
    public function index(WarehouseReceiptOrderItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $result = $this->service->index($data);

            $collection = new WarehouseReceiptOrderItemCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    /**
     * @response 200 WarehouseReceiptOrderItemResource
     */
    public function show(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeView($request, $id);

            $item = $this->service->show($id);

            if (!$item) {
                abort(404, 'Позиция приходного ордера не найдена');
            }

            return $this->successResponse(new WarehouseReceiptOrderItemResource($item));
        });
    }

    /**
     * @response 201 {"id": "uuid", "message": "Позиция приходного ордера создана"}
     */
    public function store(WarehouseReceiptOrderItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->authorizeCreate($request, $dto);

            $id = $this->service->create($dto);

            return $this->successResponse([
                'id' => $id,
                'message' => 'Позиция приходного ордера создана'
            ], 201);
        });
    }

    /**
     * @response 200 {"message": "Позиция приходного ордера обновлена"}
     */
    public function update(string $id, WarehouseReceiptOrderItemUpdateRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $dto = $request->toDTO();

            $this->authorizeUpdate($request, $dto);

            $this->service->update($dto);

            return $this->successResponse([
                'message' => 'Позиция приходного ордера обновлена'
            ]);
        });
    }

    /**
     * @response 200 {"message": "Позиция приходного ордера удалена"}
     */
    public function destroy(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);

            return $this->successResponse([
                'message' => 'Позиция приходного ордера удалена'
            ]);
        });
    }
}
