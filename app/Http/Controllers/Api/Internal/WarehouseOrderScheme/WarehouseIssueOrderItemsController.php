<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Policies\WarehouseOrderScheme\WarehouseIssueOrderItemPolicyContract;
use App\Contracts\Services\Internal\WarehouseIssueOrderItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemIndexRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemStoreRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemUpdateRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemCollection;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseIssueOrderItemsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseIssueOrderItemsServiceContract $service,
        private readonly WarehouseIssueOrderItemPolicyContract $policy
    ) {
    }

    protected function getPolicy(): WarehouseIssueOrderItemPolicyContract
    {
        return $this->policy;
    }

    /**
     * @response 200 WarehouseIssueOrderItemCollection<WarehouseIssueOrderItemResource>
     */
    public function index(WarehouseIssueOrderItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $result = $this->service->index($data);

            $collection = new WarehouseIssueOrderItemCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    /**
     * @response 200 WarehouseIssueOrderItemResource
     */
    public function show(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeView($request, $id);

            $item = $this->service->show($id);

            if (!$item) {
                abort(404, 'Позиция расходного ордера не найдена');
            }

            return $this->successResponse(new WarehouseIssueOrderItemResource($item));
        });
    }

    /**
     * @response 201 {"id": "uuid", "message": "Позиция расходного ордера создана"}
     */
    public function store(WarehouseIssueOrderItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->authorizeCreate($request, $dto);

            $id = $this->service->create($dto);

            return $this->successResponse([
                'id' => $id,
                'message' => 'Позиция расходного ордера создана'
            ], 201);
        });
    }

    /**
     * @response 200 {"message": "Позиция расходного ордера обновлена"}
     */
    public function update(string $id, WarehouseIssueOrderItemUpdateRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $dto = $request->toDTO();

            $this->authorizeUpdate($request, $dto);

            $this->service->update($dto);

            return $this->successResponse([
                'message' => 'Позиция расходного ордера обновлена'
            ]);
        });
    }

    /**
     * @response 200 {"message": "Позиция расходного ордера удалена"}
     */
    public function destroy(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);

            return $this->successResponse([
                'message' => 'Позиция расходного ордера удалена'
            ]);
        });
    }
}
