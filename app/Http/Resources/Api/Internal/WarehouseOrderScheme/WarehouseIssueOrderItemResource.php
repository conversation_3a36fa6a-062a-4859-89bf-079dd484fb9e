<?php

namespace App\Http\Resources\Api\Internal\WarehouseOrderScheme;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseIssueOrderItemResource extends JsonResource
{
    /**
     * @var string $id
     * @var string $warehouse_issue_order_id
     * @var string $warehouse_item_id
     * @var int $quantity
     * @var string $unit_price
     * @var string $total_price
     * @var string|null $comment
     * @var string $created_at
     * @var string $updated_at
     * @var object|null $warehouse_item
     * @var object|null $product
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string)$this->id,
            'warehouse_issue_order_id' => (string)$this->warehouse_issue_order_id,
            'warehouse_item_id' => (string)$this->warehouse_item_id,
            'quantity' => (int)$this->quantity,
            'unit_price' => (string)$this->unit_price,
            'total_price' => (string)$this->total_price,
            'comment' => $this->comment ? (string)$this->comment : null,
            'created_at' => (string)$this->created_at,
            'updated_at' => (string)$this->updated_at,
            'warehouse_item' => $this->when(isset($this->warehouse_item), function () {
                return is_string($this->warehouse_item) ? json_decode($this->warehouse_item, true) : $this->warehouse_item;
            }),
            'product' => $this->when(isset($this->product), function () {
                return is_string($this->product) ? json_decode($this->product, true) : $this->product;
            }),
        ];
    }
}
