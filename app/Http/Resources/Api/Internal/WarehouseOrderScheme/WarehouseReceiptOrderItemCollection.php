<?php

namespace App\Http\Resources\Api\Internal\WarehouseOrderScheme;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class WarehouseReceiptOrderItemCollection extends ResourceCollection
{
    public $collects = WarehouseReceiptOrderItemResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
