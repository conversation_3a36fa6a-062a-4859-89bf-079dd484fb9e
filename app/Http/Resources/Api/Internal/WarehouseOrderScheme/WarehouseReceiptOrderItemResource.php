<?php

namespace App\Http\Resources\Api\Internal\WarehouseOrderScheme;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseReceiptOrderItemResource extends JsonResource
{
    /**
     * @var string $id
     * @var string $warehouse_receipt_order_id
     * @var string $product_id
     * @var int $quantity
     * @var string $unit_price
     * @var string $total_price
     * @var string|null $batch_number
     * @var string|null $lot_number
     * @var string|null $expiry_date
     * @var string|null $supplier_batch
     * @var string $quality_status
     * @var string|null $storage_location
     * @var string|null $comment
     * @var string $created_at
     * @var string $updated_at
     * @var object|null $product
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string)$this->id,
            'warehouse_receipt_order_id' => (string)$this->warehouse_receipt_order_id,
            'product_id' => (string)$this->product_id,
            'quantity' => (int)$this->quantity,
            'unit_price' => (string)$this->unit_price,
            'total_price' => (string)$this->total_price,
            'batch_number' => $this->batch_number ? (string)$this->batch_number : null,
            'lot_number' => $this->lot_number ? (string)$this->lot_number : null,
            'expiry_date' => $this->expiry_date ? (string)$this->expiry_date : null,
            'supplier_batch' => $this->supplier_batch ? (string)$this->supplier_batch : null,
            'quality_status' => (string)$this->quality_status,
            'storage_location' => $this->storage_location ? (string)$this->storage_location : null,
            'comment' => $this->comment ? (string)$this->comment : null,
            'created_at' => (string)$this->created_at,
            'updated_at' => (string)$this->updated_at,
            'product' => $this->when(isset($this->product), function () {
                return is_string($this->product) ? json_decode($this->product, true) : $this->product;
            }),
        ];
    }
}
