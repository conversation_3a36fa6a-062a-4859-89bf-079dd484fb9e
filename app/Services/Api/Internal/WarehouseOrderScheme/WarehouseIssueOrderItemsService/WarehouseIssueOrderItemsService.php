<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\WarehouseIssueOrderItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers\WarehouseIssueOrderItemsCreateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers\WarehouseIssueOrderItemsDeleteHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers\WarehouseIssueOrderItemsIndexHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers\WarehouseIssueOrderItemsShowHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers\WarehouseIssueOrderItemsUpdateHandler;
use Illuminate\Support\Collection;

readonly class WarehouseIssueOrderItemsService implements WarehouseIssueOrderItemsServiceContract
{
    public function __construct(
        private WarehouseIssueOrderItemsIndexHandler $indexHandler,
        private WarehouseIssueOrderItemsShowHandler $showHandler,
        private WarehouseIssueOrderItemsCreateHandler $createHandler,
        private WarehouseIssueOrderItemsUpdateHandler $updateHandler,
        private WarehouseIssueOrderItemsDeleteHandler $deleteHandler
    ) {
    }

    public function index(IndexRequestDTO $data): Collection
    {
        return $this->indexHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
