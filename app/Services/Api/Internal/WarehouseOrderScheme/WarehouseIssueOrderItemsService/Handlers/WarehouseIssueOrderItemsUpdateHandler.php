<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderItemUpdateDTO;
use RuntimeException;

readonly class WarehouseIssueOrderItemsUpdateHandler
{
    public function __construct(
        private WarehouseIssueOrderItemsRepositoryContract $repository,
        private WarehouseIssueOrdersRepositoryContract $ordersRepository,
        private WarehouseItemsRepositoryContract $warehouseItemsRepository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseIssueOrderItemUpdateDTO) {
            throw new RuntimeException('Invalid DTO type');
        }

        $item = $this->repository->show($dto->resourceId);
        if (!$item) {
            throw new RuntimeException('Позиция расходного ордера не найдена');
        }

        $order = $this->ordersRepository->show($item->warehouse_issue_order_id);
        if ($order && $order->held) {
            throw new RuntimeException('Нельзя изменять позиции проведенного расходного ордера');
        }

        $warehouseItem = $this->warehouseItemsRepository->show($item->warehouse_item_id);
        if (!$warehouseItem) {
            throw new RuntimeException('Партия товара не найдена');
        }

        $availableQuantity = $warehouseItem->available_quantity + $item->quantity;
        if ($availableQuantity < $dto->quantity) {
            throw new RuntimeException('Недостаточно товара в наличии');
        }

        $this->repository->update($dto->resourceId, [
            'quantity' => $dto->quantity,
            'unit_price' => $dto->unitPrice,
            'total_price' => $dto->totalPrice,
            'comment' => $dto->comment,
        ]);

        $this->updateOrderTotals($item->warehouse_issue_order_id);
    }

    private function updateOrderTotals(string $orderId): void
    {
        $items = $this->repository->getByOrderId($orderId);
        
        $totalQuantity = $items->sum('quantity');
        $totalCost = $items->sum(function ($item) {
            return (float)$item->total_price;
        });

        $this->ordersRepository->update($orderId, [
            'total_quantity' => $totalQuantity,
            'total_cost' => (string)$totalCost,
        ]);
    }
}
