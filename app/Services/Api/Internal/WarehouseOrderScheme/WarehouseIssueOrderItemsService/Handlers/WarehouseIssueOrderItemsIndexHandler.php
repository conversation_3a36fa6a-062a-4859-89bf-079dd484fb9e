<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehouseIssueOrderItemsIndexHandler
{
    public function __construct(
        private WarehouseIssueOrderItemsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
