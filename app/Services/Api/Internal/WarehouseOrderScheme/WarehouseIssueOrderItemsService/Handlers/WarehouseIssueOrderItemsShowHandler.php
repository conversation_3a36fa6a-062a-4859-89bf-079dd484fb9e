<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;

readonly class WarehouseIssueOrderItemsShowHandler
{
    public function __construct(
        private WarehouseIssueOrderItemsRepositoryContract $repository
    ) {
    }

    public function run(string $id): ?object
    {
        return $this->repository->show($id);
    }
}
