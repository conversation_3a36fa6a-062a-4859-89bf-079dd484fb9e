<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderItemStoreDTO;
use App\Traits\HasOrderedUuid;
use RuntimeException;

readonly class WarehouseIssueOrderItemsCreateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private WarehouseIssueOrderItemsRepositoryContract $repository,
        private WarehouseIssueOrdersRepositoryContract $ordersRepository,
        private WarehouseItemsRepositoryContract $warehouseItemsRepository
    ) {
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof WarehouseIssueOrderItemStoreDTO) {
            throw new RuntimeException('Invalid DTO type');
        }

        $resourceId = $this->generateUuid();

        $order = $this->ordersRepository->show($dto->warehouseIssueOrderId);
        if (!$order) {
            throw new RuntimeException('Расходный ордер не найден');
        }

        if ($order->held) {
            throw new RuntimeException('Нельзя добавлять позиции в проведенный расходный ордер');
        }

        $warehouseItem = $this->warehouseItemsRepository->show($dto->warehouseItemId);
        if (!$warehouseItem) {
            throw new RuntimeException('Партия товара не найдена');
        }

        if ($warehouseItem->available_quantity < $dto->quantity) {
            throw new RuntimeException('Недостаточно товара в наличии');
        }

        $this->repository->insert([
            'id' => $resourceId,
            'warehouse_issue_order_id' => $dto->warehouseIssueOrderId,
            'warehouse_item_id' => $dto->warehouseItemId,
            'quantity' => $dto->quantity,
            'unit_price' => $dto->unitPrice,
            'total_price' => $dto->totalPrice,
            'comment' => $dto->comment,
        ]);

        $this->updateOrderTotals($dto->warehouseIssueOrderId);

        return $resourceId;
    }

    private function updateOrderTotals(string $orderId): void
    {
        $items = $this->repository->getByOrderId($orderId);
        
        $totalQuantity = $items->sum('quantity');
        $totalCost = $items->sum(function ($item) {
            return (float)$item->total_price;
        });

        $this->ordersRepository->update($orderId, [
            'total_quantity' => $totalQuantity,
            'total_cost' => (string)$totalCost,
        ]);
    }
}
