<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderItemsService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use RuntimeException;

readonly class WarehouseIssueOrderItemsDeleteHandler
{
    public function __construct(
        private WarehouseIssueOrderItemsRepositoryContract $repository,
        private WarehouseIssueOrdersRepositoryContract $ordersRepository
    ) {
    }

    public function run(string $id): void
    {
        $item = $this->repository->show($id);
        if (!$item) {
            throw new RuntimeException('Позиция расходного ордера не найдена');
        }

        $order = $this->ordersRepository->show($item->warehouse_issue_order_id);
        if ($order && $order->held) {
            throw new RuntimeException('Нельзя удалять позиции проведенного расходного ордера');
        }

        $this->repository->delete($id);

        $this->updateOrderTotals($item->warehouse_issue_order_id);
    }

    private function updateOrderTotals(string $orderId): void
    {
        $items = $this->repository->getByOrderId($orderId);
        
        $totalQuantity = $items->sum('quantity');
        $totalCost = $items->sum(function ($item) {
            return (float)$item->total_price;
        });

        $this->ordersRepository->update($orderId, [
            'total_quantity' => $totalQuantity,
            'total_cost' => (string)$totalCost,
        ]);
    }
}
