<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\WarehouseReceiptOrderItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsCreateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsDeleteHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsIndexHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsShowHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsUpdateHandler;
use Illuminate\Support\Collection;

readonly class WarehouseReceiptOrderItemsService implements WarehouseReceiptOrderItemsServiceContract
{
    public function __construct(
        private WarehouseReceiptOrderItemsIndexHandler $indexHandler,
        private WarehouseReceiptOrderItemsShowHandler $showHandler,
        private WarehouseReceiptOrderItemsCreateHandler $createHandler,
        private WarehouseReceiptOrderItemsUpdateHandler $updateHandler,
        private WarehouseReceiptOrderItemsDeleteHandler $deleteHandler
    ) {
    }

    public function index(IndexRequestDTO $data): Collection
    {
        return $this->indexHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
