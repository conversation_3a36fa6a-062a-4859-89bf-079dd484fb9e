<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemStoreDTO;
use App\Traits\HasOrderedUuid;
use RuntimeException;

readonly class WarehouseReceiptOrderItemsCreateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private WarehouseReceiptOrderItemsRepositoryContract $repository,
        private WarehouseReceiptOrdersRepositoryContract $ordersRepository,
        private WarehouseTransactionServiceContract $transactionService
    ) {
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof WarehouseReceiptOrderItemStoreDTO) {
            throw new RuntimeException('Invalid DTO type');
        }

        $resourceId = $this->generateUuid();

        $order = $this->ordersRepository->show($dto->warehouseReceiptOrderId);
        if (!$order) {
            throw new RuntimeException('Приходный ордер не найден');
        }

        if ($order->held) {
            throw new RuntimeException('Нельзя добавлять позиции в проведенный приходный ордер');
        }

        $this->repository->insert([
            'id' => $resourceId,
            'warehouse_receipt_order_id' => $dto->warehouseReceiptOrderId,
            'product_id' => $dto->productId,
            'quantity' => $dto->quantity,
            'unit_price' => $dto->unitPrice,
            'total_price' => $dto->totalPrice,
            'batch_number' => $dto->batchNumber,
            'lot_number' => $dto->lotNumber,
            'expiry_date' => $dto->expiryDate,
            'supplier_batch' => $dto->supplierBatch,
            'quality_status' => $dto->qualityStatus,
            'storage_location' => $dto->storageLocation,
            'comment' => $dto->comment,
        ]);

        $this->updateOrderTotals($dto->warehouseReceiptOrderId);

        return $resourceId;
    }

    private function updateOrderTotals(string $orderId): void
    {
        $items = $this->repository->getByOrderId($orderId);
        
        $totalQuantity = $items->sum('quantity');
        $totalCost = $items->sum(function ($item) {
            return (float)$item->total_price;
        });

        $this->ordersRepository->update($orderId, [
            'total_quantity' => $totalQuantity,
            'total_cost' => (string)$totalCost,
        ]);
    }
}
