<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;

readonly class WarehouseReceiptOrderItemsShowHandler
{
    public function __construct(
        private WarehouseReceiptOrderItemsRepositoryContract $repository
    ) {
    }

    public function run(string $id): ?object
    {
        return $this->repository->show($id);
    }
}
