<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemUpdateDTO;
use RuntimeException;

readonly class WarehouseReceiptOrderItemsUpdateHandler
{
    public function __construct(
        private WarehouseReceiptOrderItemsRepositoryContract $repository,
        private WarehouseReceiptOrdersRepositoryContract $ordersRepository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseReceiptOrderItemUpdateDTO) {
            throw new RuntimeException('Invalid DTO type');
        }

        $item = $this->repository->show($dto->resourceId);
        if (!$item) {
            throw new RuntimeException('Позиция приходного ордера не найдена');
        }

        $order = $this->ordersRepository->show($item->warehouse_receipt_order_id);
        if ($order && $order->held) {
            throw new RuntimeException('Нельзя изменять позиции проведенного приходного ордера');
        }

        $this->repository->update($dto->resourceId, [
            'quantity' => $dto->quantity,
            'unit_price' => $dto->unitPrice,
            'total_price' => $dto->totalPrice,
            'batch_number' => $dto->batchNumber,
            'lot_number' => $dto->lotNumber,
            'expiry_date' => $dto->expiryDate,
            'supplier_batch' => $dto->supplierBatch,
            'quality_status' => $dto->qualityStatus,
            'storage_location' => $dto->storageLocation,
            'comment' => $dto->comment,
        ]);

        $this->updateOrderTotals($item->warehouse_receipt_order_id);
    }

    private function updateOrderTotals(string $orderId): void
    {
        $items = $this->repository->getByOrderId($orderId);
        
        $totalQuantity = $items->sum('quantity');
        $totalCost = $items->sum(function ($item) {
            return (float)$item->total_price;
        });

        $this->ordersRepository->update($orderId, [
            'total_quantity' => $totalQuantity,
            'total_cost' => (string)$totalCost,
        ]);
    }
}
