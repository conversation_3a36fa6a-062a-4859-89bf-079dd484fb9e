<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehouseReceiptOrderItemsIndexHandler
{
    public function __construct(
        private WarehouseReceiptOrderItemsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
