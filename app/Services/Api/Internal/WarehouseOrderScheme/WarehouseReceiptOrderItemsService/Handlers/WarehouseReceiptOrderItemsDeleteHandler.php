<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use RuntimeException;

readonly class WarehouseReceiptOrderItemsDeleteHandler
{
    public function __construct(
        private WarehouseReceiptOrderItemsRepositoryContract $repository,
        private WarehouseReceiptOrdersRepositoryContract $ordersRepository
    ) {
    }

    public function run(string $id): void
    {
        $item = $this->repository->show($id);
        if (!$item) {
            throw new RuntimeException('Позиция приходного ордера не найдена');
        }

        $order = $this->ordersRepository->show($item->warehouse_receipt_order_id);
        if ($order && $order->held) {
            throw new RuntimeException('Нельзя удалять позиции проведенного приходного ордера');
        }

        $this->repository->delete($id);

        $this->updateOrderTotals($item->warehouse_receipt_order_id);
    }

    private function updateOrderTotals(string $orderId): void
    {
        $items = $this->repository->getByOrderId($orderId);
        
        $totalQuantity = $items->sum('quantity');
        $totalCost = $items->sum(function ($item) {
            return (float)$item->total_price;
        });

        $this->ordersRepository->update($orderId, [
            'total_quantity' => $totalQuantity,
            'total_cost' => (string)$totalCost,
        ]);
    }
}
