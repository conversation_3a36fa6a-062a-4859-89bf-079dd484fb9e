<?php

namespace Tests\Unit\Services;

use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemStoreDTO;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderItemUpdateDTO;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsCreateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsUpdateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderItemsService\Handlers\WarehouseReceiptOrderItemsDeleteHandler;
use Illuminate\Support\Collection;
use Mockery;
use RuntimeException;
use Tests\TestCase;

class WarehouseReceiptOrderItemsServiceTest extends TestCase
{
    protected $itemsRepository;
    protected $ordersRepository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->itemsRepository = Mockery::mock(WarehouseReceiptOrderItemsRepositoryContract::class);
        $this->ordersRepository = Mockery::mock(WarehouseReceiptOrdersRepositoryContract::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_create_handler_creates_item_successfully(): void
    {
        // Arrange
        $handler = new WarehouseReceiptOrderItemsCreateHandler(
            $this->itemsRepository,
            $this->ordersRepository,
            Mockery::mock(\App\Contracts\Services\Internal\WarehouseTransactionServiceContract::class)
        );

        $dto = new WarehouseReceiptOrderItemStoreDTO(
            warehouseReceiptOrderId: 'order-id',
            productId: 'product-id',
            quantity: 100,
            unitPrice: '150.00',
            totalPrice: '15000.00',
            userId: 1
        );

        $order = (object) [
            'id' => 'order-id',
            'held' => false
        ];

        $items = collect([
            (object) ['quantity' => 100, 'total_price' => '15000.00']
        ]);

        // Expectations
        $this->ordersRepository
            ->shouldReceive('show')
            ->with('order-id')
            ->once()
            ->andReturn($order);

        $this->itemsRepository
            ->shouldReceive('insert')
            ->once()
            ->andReturn(true);

        $this->itemsRepository
            ->shouldReceive('getByOrderId')
            ->with('order-id')
            ->once()
            ->andReturn($items);

        $this->ordersRepository
            ->shouldReceive('update')
            ->with('order-id', [
                'total_quantity' => 100,
                'total_cost' => '15000'
            ])
            ->once()
            ->andReturn(1);

        // Act
        $result = $handler->run($dto);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    public function test_create_handler_throws_exception_for_held_order(): void
    {
        // Arrange
        $handler = new WarehouseReceiptOrderItemsCreateHandler(
            $this->itemsRepository,
            $this->ordersRepository,
            Mockery::mock(\App\Contracts\Services\Internal\WarehouseTransactionServiceContract::class)
        );

        $dto = new WarehouseReceiptOrderItemStoreDTO(
            warehouseReceiptOrderId: 'order-id',
            productId: 'product-id',
            quantity: 100,
            unitPrice: '150.00',
            totalPrice: '15000.00',
            userId: 1
        );

        $order = (object) [
            'id' => 'order-id',
            'held' => true
        ];

        // Expectations
        $this->ordersRepository
            ->shouldReceive('show')
            ->with('order-id')
            ->once()
            ->andReturn($order);

        // Act & Assert
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Нельзя добавлять позиции в проведенный приходный ордер');

        $handler->run($dto);
    }

    public function test_update_handler_updates_item_successfully(): void
    {
        // Arrange
        $handler = new WarehouseReceiptOrderItemsUpdateHandler(
            $this->itemsRepository,
            $this->ordersRepository
        );

        $dto = new WarehouseReceiptOrderItemUpdateDTO(
            resourceId: 'item-id',
            quantity: 150,
            unitPrice: '120.00',
            totalPrice: '18000.00',
            userId: 1
        );

        $item = (object) [
            'id' => 'item-id',
            'warehouse_receipt_order_id' => 'order-id'
        ];

        $order = (object) [
            'id' => 'order-id',
            'held' => false
        ];

        $items = collect([
            (object) ['quantity' => 150, 'total_price' => '18000.00']
        ]);

        // Expectations
        $this->itemsRepository
            ->shouldReceive('show')
            ->with('item-id')
            ->once()
            ->andReturn($item);

        $this->ordersRepository
            ->shouldReceive('show')
            ->with('order-id')
            ->once()
            ->andReturn($order);

        $this->itemsRepository
            ->shouldReceive('update')
            ->with('item-id', Mockery::type('array'))
            ->once()
            ->andReturn(1);

        $this->itemsRepository
            ->shouldReceive('getByOrderId')
            ->with('order-id')
            ->once()
            ->andReturn($items);

        $this->ordersRepository
            ->shouldReceive('update')
            ->with('order-id', [
                'total_quantity' => 150,
                'total_cost' => '18000'
            ])
            ->once()
            ->andReturn(1);

        // Act
        $handler->run($dto);

        // Assert - no exception thrown
        $this->assertTrue(true);
    }

    public function test_delete_handler_deletes_item_successfully(): void
    {
        // Arrange
        $handler = new WarehouseReceiptOrderItemsDeleteHandler(
            $this->itemsRepository,
            $this->ordersRepository
        );

        $item = (object) [
            'id' => 'item-id',
            'warehouse_receipt_order_id' => 'order-id'
        ];

        $order = (object) [
            'id' => 'order-id',
            'held' => false
        ];

        $items = collect(); // Пустая коллекция после удаления

        // Expectations
        $this->itemsRepository
            ->shouldReceive('show')
            ->with('item-id')
            ->once()
            ->andReturn($item);

        $this->ordersRepository
            ->shouldReceive('show')
            ->with('order-id')
            ->once()
            ->andReturn($order);

        $this->itemsRepository
            ->shouldReceive('delete')
            ->with('item-id')
            ->once()
            ->andReturn(1);

        $this->itemsRepository
            ->shouldReceive('getByOrderId')
            ->with('order-id')
            ->once()
            ->andReturn($items);

        $this->ordersRepository
            ->shouldReceive('update')
            ->with('order-id', [
                'total_quantity' => 0,
                'total_cost' => '0'
            ])
            ->once()
            ->andReturn(1);

        // Act
        $handler->run('item-id');

        // Assert - no exception thrown
        $this->assertTrue(true);
    }

    public function test_delete_handler_throws_exception_for_held_order(): void
    {
        // Arrange
        $handler = new WarehouseReceiptOrderItemsDeleteHandler(
            $this->itemsRepository,
            $this->ordersRepository
        );

        $item = (object) [
            'id' => 'item-id',
            'warehouse_receipt_order_id' => 'order-id'
        ];

        $order = (object) [
            'id' => 'order-id',
            'held' => true
        ];

        // Expectations
        $this->itemsRepository
            ->shouldReceive('show')
            ->with('item-id')
            ->once()
            ->andReturn($item);

        $this->ordersRepository
            ->shouldReceive('show')
            ->with('order-id')
            ->once()
            ->andReturn($order);

        // Act & Assert
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Нельзя удалять позиции проведенного приходного ордера');

        $handler->run('item-id');
    }

    public function test_handlers_throw_exception_for_missing_item(): void
    {
        // Arrange
        $updateHandler = new WarehouseReceiptOrderItemsUpdateHandler(
            $this->itemsRepository,
            $this->ordersRepository
        );

        $deleteHandler = new WarehouseReceiptOrderItemsDeleteHandler(
            $this->itemsRepository,
            $this->ordersRepository
        );

        $dto = new WarehouseReceiptOrderItemUpdateDTO(
            resourceId: 'non-existent-id',
            quantity: 150,
            unitPrice: '120.00',
            totalPrice: '18000.00',
            userId: 1
        );

        // Expectations
        $this->itemsRepository
            ->shouldReceive('show')
            ->with('non-existent-id')
            ->twice()
            ->andReturn(null);

        // Act & Assert - Update handler
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Позиция приходного ордера не найдена');

        $updateHandler->run($dto);

        // Act & Assert - Delete handler
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Позиция приходного ордера не найдена');

        $deleteHandler->run('non-existent-id');
    }
}
