<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseOrderScheme;
use App\Models\WarehouseReceiptOrder;
use App\Models\WarehouseReceiptOrderItem;
use App\Models\WarehouseIssueOrder;
use App\Models\WarehouseIssueOrderItem;
use App\Models\WarehouseItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseOrderSchemeIntegrationTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $employee;
    protected $cabinet;
    protected $department;
    protected $warehouse;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'owner_id' => $user->id,
        ]);
        CabinetEmployee::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Включаем ордерную схему для склада
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ]);
    }

    public function test_complete_order_scheme_workflow(): void
    {
        // 1. Создаем приходный ордер
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'ПО-001',
            'date_from' => '2024-01-15',
            'reason' => 'Поступление от поставщика',
            'total_quantity' => 0,
            'total_cost' => '0.00'
        ];

        $receiptResponse = $this->postJson('/api/internal/warehouses/receipt-orders', $receiptOrderData);
        $receiptResponse->assertStatus(201);
        $receiptOrderId = $receiptResponse->json('id');

        // 2. Добавляем позицию к приходному ордеру
        $receiptItemData = [
            'warehouse_receipt_order_id' => $receiptOrderId,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'unit_price' => '150.00',
            'total_price' => '15000.00',
            'batch_number' => 'BATCH001',
            'quality_status' => 'good'
        ];

        $itemResponse = $this->postJson('/api/internal/warehouses/receipt-order-items', $receiptItemData);
        $itemResponse->assertStatus(201);

        // 3. Проверяем, что итоги ордера обновились
        $this->assertDatabaseHas('warehouse_receipt_orders', [
            'id' => $receiptOrderId,
            'total_quantity' => 100,
            'total_cost' => '15000.00'
        ]);

        // 4. Проводим приходный ордер
        $holdResponse = $this->postJson("/api/internal/warehouses/receipt-orders/{$receiptOrderId}/hold");
        $holdResponse->assertStatus(200);

        // 5. Проверяем, что создались warehouse_items
        $this->assertDatabaseHas('warehouse_items', [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'batch_number' => 'BATCH001'
        ]);

        // 6. Получаем созданную партию товара
        $warehouseItem = WarehouseItem::where([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'batch_number' => 'BATCH001'
        ])->first();

        $this->assertNotNull($warehouseItem);

        // 7. Создаем расходный ордер
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-001',
            'date_from' => '2024-01-16',
            'write_off_reason' => 'defective',
            'reason_description' => 'Обнаружен брак',
            'total_quantity' => 0,
            'total_cost' => '0.00'
        ];

        $issueResponse = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);
        $issueResponse->assertStatus(201);
        $issueOrderId = $issueResponse->json('id');

        // 8. Добавляем позицию к расходному ордеру
        $issueItemData = [
            'warehouse_issue_order_id' => $issueOrderId,
            'warehouse_item_id' => $warehouseItem->id,
            'quantity' => 10,
            'unit_price' => '150.00',
            'total_price' => '1500.00',
            'comment' => 'Списание брака'
        ];

        $issueItemResponse = $this->postJson('/api/internal/warehouses/issue-order-items', $issueItemData);
        $issueItemResponse->assertStatus(201);

        // 9. Проверяем, что итоги расходного ордера обновились
        $this->assertDatabaseHas('warehouse_issue_orders', [
            'id' => $issueOrderId,
            'total_quantity' => 10,
            'total_cost' => '1500.00'
        ]);

        // 10. Проводим расходный ордер
        $issueHoldResponse = $this->postJson("/api/internal/warehouses/issue-orders/{$issueOrderId}/hold");
        $issueHoldResponse->assertStatus(200);

        // 11. Проверяем, что количество в партии уменьшилось
        $warehouseItem->refresh();
        $this->assertEquals(90, $warehouseItem->quantity);
        $this->assertEquals(90, $warehouseItem->available_quantity);
    }

    public function test_cannot_modify_items_after_order_is_held(): void
    {
        // Arrange - создаем и проводим ордер
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => true
        ]);

        $item = WarehouseReceiptOrderItem::factory()->create([
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $this->product->id
        ]);

        // Act & Assert - нельзя изменить позицию
        $updateData = ['quantity' => 200];
        $response = $this->putJson("/api/internal/warehouses/receipt-order-items/{$item->id}", $updateData);
        $response->assertStatus(422);

        // Act & Assert - нельзя удалить позицию
        $deleteResponse = $this->deleteJson("/api/internal/warehouses/receipt-order-items/{$item->id}");
        $deleteResponse->assertStatus(422);
    }

    public function test_validates_order_scheme_is_active(): void
    {
        // Arrange - отключаем ордерную схему
        WarehouseOrderScheme::where('warehouse_id', $this->warehouse->id)->delete();

        // Act & Assert - нельзя создать приходный ордер
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'ПО-001',
            'date_from' => '2024-01-15',
            'total_quantity' => 0,
            'total_cost' => '0.00'
        ];

        $response = $this->postJson('/api/internal/warehouses/receipt-orders', $receiptOrderData);
        $response->assertStatus(422);
        $response->assertJsonFragment(['message' => 'Склад не работает в ордерной схеме для приемок']);
    }

    public function test_order_totals_recalculation_on_item_changes(): void
    {
        // Arrange - создаем ордер с позициями
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'total_quantity' => 150,
            'total_cost' => '22500.00',
            'held' => false
        ]);

        $item1 = WarehouseReceiptOrderItem::factory()->create([
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'total_price' => '15000.00'
        ]);

        $item2 = WarehouseReceiptOrderItem::factory()->create([
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'total_price' => '7500.00'
        ]);

        // Act - обновляем первую позицию
        $updateData = [
            'quantity' => 120,
            'unit_price' => '150.00',
            'total_price' => '18000.00'
        ];

        $this->putJson("/api/internal/warehouses/receipt-order-items/{$item1->id}", $updateData);

        // Assert - проверяем пересчет итогов
        $this->assertDatabaseHas('warehouse_receipt_orders', [
            'id' => $receiptOrder->id,
            'total_quantity' => 170, // 120 + 50
            'total_cost' => '25500.00' // 18000 + 7500
        ]);

        // Act - удаляем вторую позицию
        $this->deleteJson("/api/internal/warehouses/receipt-order-items/{$item2->id}");

        // Assert - проверяем пересчет после удаления
        $this->assertDatabaseHas('warehouse_receipt_orders', [
            'id' => $receiptOrder->id,
            'total_quantity' => 120,
            'total_cost' => '18000.00'
        ]);
    }

    public function test_validates_item_creation_permissions(): void
    {
        // Arrange - создаем ордер в другом кабинете
        $otherCabinet = Cabinet::factory()->create();
        $otherWarehouse = Warehouse::factory()->create(['cabinet_id' => $otherCabinet->id]);

        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $otherCabinet->id,
            'warehouse_id' => $otherWarehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $itemData = [
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'unit_price' => '150.00',
            'total_price' => '15000.00'
        ];

        // Act & Assert - должна быть ошибка доступа
        $response = $this->postJson('/api/internal/warehouses/receipt-order-items', $itemData);
        $response->assertStatus(403);
    }

    public function test_validates_required_fields_for_items(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Act & Assert - отсутствуют обязательные поля
        $invalidData = [
            'warehouse_receipt_order_id' => $receiptOrder->id,
            // отсутствует product_id, quantity, unit_price, total_price
        ];

        $response = $this->postJson('/api/internal/warehouses/receipt-order-items', $invalidData);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['product_id', 'quantity', 'unit_price', 'total_price']);
    }

    public function test_validates_price_format_for_items(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Act & Assert - неверный формат цены
        $invalidData = [
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'unit_price' => 'invalid-price',
            'total_price' => 'invalid-total'
        ];

        $response = $this->postJson('/api/internal/warehouses/receipt-order-items', $invalidData);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['unit_price', 'total_price']);
    }

    public function test_pagination_works_for_order_items(): void
    {
        // Arrange - создаем ордер с множеством позиций
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Создаем 25 позиций
        for ($i = 1; $i <= 25; $i++) {
            WarehouseReceiptOrderItem::factory()->create([
                'warehouse_receipt_order_id' => $receiptOrder->id,
                'product_id' => $this->product->id,
                'quantity' => $i,
                'unit_price' => '100.00',
                'total_price' => ($i * 100) . '.00'
            ]);
        }

        // Act - запрашиваем первую страницу
        $response = $this->getJson("/api/internal/warehouses/receipt-orders/{$receiptOrder->id}/items?per_page=10&page=1");

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'total',
                'page',
                'per_page',
                'last_page'
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals(25, $responseData['meta']['total']);
        $this->assertEquals(1, $responseData['meta']['page']);
        $this->assertEquals(10, $responseData['meta']['per_page']);
        $this->assertEquals(3, $responseData['meta']['last_page']);
        $this->assertCount(10, $responseData['data']);
    }

    public function test_search_functionality_for_order_items(): void
    {
        // Arrange - создаем продукты с разными названиями
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Товар для поиска'
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Другой товар'
        ]);

        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        WarehouseReceiptOrderItem::factory()->create([
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $product1->id
        ]);

        WarehouseReceiptOrderItem::factory()->create([
            'warehouse_receipt_order_id' => $receiptOrder->id,
            'product_id' => $product2->id
        ]);

        // Act - поиск по названию товара
        $response = $this->getJson("/api/internal/warehouses/receipt-orders/{$receiptOrder->id}/items?filters[search][value]=поиска");

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertEquals(1, count($responseData['data']));
    }
}
